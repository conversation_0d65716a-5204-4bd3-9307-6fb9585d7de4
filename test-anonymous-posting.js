const axios = require('axios');

// Test script untuk memverifikasi fitur anonymous posting
async function testAnonymousPosting() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('🧪 Testing Anonymous Posting Feature...\n');
  
  try {
    // Step 1: Login untuk mendapatkan token
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>', // Ganti dengan email yang valid
      password: 'password123'    // Ganti dengan password yang valid
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Step 2: Test posting dengan anonymous = false
    console.log('\n2. 📝 Testing normal post (not anonymous)...');
    const normalPost = await axios.post(`${baseURL}/safespace/posts`, {
      content: 'This is a normal post - should show my name',
      is_anonymous: false
    }, { headers });
    
    console.log('✅ Normal post created:');
    console.log(`   - ID: ${normalPost.data.id}`);
    console.log(`   - Author: ${normalPost.data.author.name}`);
    console.log(`   - Is Anonymous: ${normalPost.data.isAnonymous}`);
    console.log(`   - Content: ${normalPost.data.content}`);
    
    // Step 3: Test posting dengan anonymous = true
    console.log('\n3. 🕶️ Testing anonymous post...');
    const anonymousPost = await axios.post(`${baseURL}/safespace/posts`, {
      content: 'This is an anonymous post - should show as Anonim',
      is_anonymous: true
    }, { headers });
    
    console.log('✅ Anonymous post created:');
    console.log(`   - ID: ${anonymousPost.data.id}`);
    console.log(`   - Author: ${anonymousPost.data.author.name}`);
    console.log(`   - Is Anonymous: ${anonymousPost.data.isAnonymous}`);
    console.log(`   - Content: ${anonymousPost.data.content}`);
    console.log(`   - Avatar: ${anonymousPost.data.author.avatar}`);
    
    // Step 4: Verify posts in database by fetching all posts
    console.log('\n4. 📋 Fetching all posts to verify...');
    const allPosts = await axios.get(`${baseURL}/safespace/posts`, { headers });
    
    console.log(`✅ Found ${allPosts.data.length} posts:`);
    allPosts.data.slice(0, 5).forEach((post, index) => {
      console.log(`   ${index + 1}. ${post.isAnonymous ? '🕶️' : '👤'} ${post.author.name}: "${post.content.substring(0, 50)}..."`);
    });
    
    // Step 5: Test specific post retrieval
    console.log('\n5. 🔍 Testing specific post retrieval...');
    const specificPost = await axios.get(`${baseURL}/safespace/posts/${anonymousPost.data.id}`, { headers });
    
    console.log('✅ Retrieved anonymous post:');
    console.log(`   - Author: ${specificPost.data.author.name}`);
    console.log(`   - Is Anonymous: ${specificPost.data.isAnonymous}`);
    console.log(`   - Avatar: ${specificPost.data.author.avatar}`);
    
    // Verification
    console.log('\n🎯 VERIFICATION RESULTS:');
    console.log('=========================');
    
    const normalPostCorrect = !normalPost.data.isAnonymous && normalPost.data.author.name !== 'Anonim';
    const anonymousPostCorrect = anonymousPost.data.isAnonymous && anonymousPost.data.author.name === 'Anonim' && anonymousPost.data.author.avatar === null;
    
    console.log(`✅ Normal post correct: ${normalPostCorrect ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Anonymous post correct: ${anonymousPostCorrect ? 'PASS' : 'FAIL'}`);
    
    if (normalPostCorrect && anonymousPostCorrect) {
      console.log('\n🎉 ALL TESTS PASSED! Anonymous posting feature is working correctly.');
    } else {
      console.log('\n❌ SOME TESTS FAILED! Please check the implementation.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure you have a valid user account and update the login credentials in this script.');
    }
  }
}

// Run the test
testAnonymousPosting();
