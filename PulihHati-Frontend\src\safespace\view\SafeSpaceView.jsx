import PropTypes from 'prop-types';
import { useState, useEffect, useCallback, useMemo } from 'react';
import NavigationTabs from './components/NavigationTabs';
import HomeTab from './components/HomeTab';
import ProfileTab from './components/ProfileTab';
import NotificationsTab from './components/NotificationsTab';
import PostModal from './components/PostModal';

function SafeSpaceView({ presenter }) {
  const [state, setState] = useState({
    activeTab: 'home',
    posts: [],
    loading: true,
    error: null,
    newPost: '',
    newComment: '',
    selectedPost: null,
    user: {},
    bookmarkAnimations: {},
    inlineComments: {},
    notifications: [],
    unreadCount: 0,
    processingNotification: null
  });

  // Memoize presenter initialization to prevent unnecessary re-initializations
  const initializePresenter = useCallback(() => {
    presenter.setStateUpdater(setState);
    presenter.initialize();
  }, [presenter]);

  useEffect(() => {
    initializePresenter();
  }, [initializePresenter]);

  const {
    activeTab,
    posts,
    loading,
    error,
    newPost,
    newComment,
    selectedPost,
    user,
    bookmarkAnimations,
    inlineComments,
    notifications,
    unreadCount,
    processingNotification
  } = state;

  // Memoize handler functions to prevent unnecessary re-renders
  const handleTabChange = useCallback((tab) => {
    presenter.setActiveTab(tab);
  }, [presenter]);

  const handleNewPost = useCallback((e, is_anonymous) => {
    presenter.handleNewPost(e, !!is_anonymous);
  }, [presenter]);

  const handleNewPostChange = useCallback((text) => {
    presenter.updateNewPost(text);
  }, [presenter]);

  const handleLike = useCallback((postId) => {
    presenter.toggleLike(postId);
  }, [presenter]);

  const handleBookmark = useCallback((postId) => {
    presenter.handleBookmark(postId);
  }, [presenter]);

  const handleCommentClick = useCallback((post) => {
    presenter.openCommentModal(post);
  }, [presenter]);

  const handleInlineComment = useCallback((e, postId) => {
    presenter.handleInlineComment(e, postId);
  }, [presenter]);

  const handleInlineCommentChange = useCallback((postId, text) => {
    presenter.updateInlineComment(postId, text);
  }, [presenter]);

  const handleAvatarUpload = useCallback((file) => {
    presenter.handleAvatarUpload(file);
  }, [presenter]);

  const handleMarkAllAsRead = useCallback(() => {
    presenter.handleMarkAllAsRead();
  }, [presenter]);

  const handleNotificationClick = useCallback((notification) => {
    presenter.handleNotificationClick(notification);
  }, [presenter]);

  const handleCloseModal = useCallback(() => {
    presenter.closeCommentModal();
  }, [presenter]);

  const handleNewComment = useCallback((e) => {
    presenter.handleNewComment(e);
  }, [presenter]);

  const handleNewCommentChange = useCallback((text) => {
    presenter.updateNewComment(text);
  }, [presenter]);

  // Memoize filtered posts to prevent unnecessary recalculations
  const filteredPosts = useMemo(() => {
    if (activeTab === 'saved') {
      return posts.filter(post => post.bookmarked);
    }
    return posts;
  }, [posts, activeTab]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-stone-50 to-stone-100">
      <div className="max-w-5xl mx-auto px-4 py-6 mt-16">
        <NavigationTabs
          activeTab={activeTab}
          unreadCount={unreadCount}
          onTabChange={handleTabChange}
        />

        <main>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          {(activeTab === 'home' || activeTab === 'saved') && (
            <HomeTab
              activeTab={activeTab}
              posts={filteredPosts}
              loading={loading}
              newPost={newPost}
              user={user}
              bookmarkAnimations={bookmarkAnimations}
              inlineComments={inlineComments}
              onNewPost={handleNewPost}
              onNewPostChange={handleNewPostChange}
              onLike={handleLike}
              onBookmark={handleBookmark}
              onCommentClick={handleCommentClick}
              onInlineComment={handleInlineComment}
              onInlineCommentChange={handleInlineCommentChange}
              getRecentComments={(comments) => presenter.getRecentComments(comments)}
            />
          )}

          {activeTab === 'profile' && !loading && (
            <ProfileTab
              user={user}
              posts={posts}
              onAvatarUpload={handleAvatarUpload}
            />
          )}

          {activeTab === 'notifications' && (
            <NotificationsTab
              notifications={notifications}
              loading={loading}
              unreadCount={unreadCount}
              processingNotification={processingNotification}
              onMarkAllAsRead={handleMarkAllAsRead}
              onNotificationClick={handleNotificationClick}
            />
          )}
        </main>
      </div>

      {selectedPost && (
        <PostModal
          post={selectedPost}
          user={user}
          newComment={newComment}
          onClose={handleCloseModal}
          onNewComment={handleNewComment}
          onNewCommentChange={handleNewCommentChange}
        />
      )}
    </div>
  );
}

SafeSpaceView.propTypes = {
  presenter: PropTypes.shape({
    setStateUpdater: PropTypes.func.isRequired,
    initialize: PropTypes.func.isRequired,
    setActiveTab: PropTypes.func.isRequired,
    handleNewPost: PropTypes.func.isRequired,
    updateNewPost: PropTypes.func.isRequired,
    toggleLike: PropTypes.func.isRequired,
    handleBookmark: PropTypes.func.isRequired,
    openCommentModal: PropTypes.func.isRequired,
    handleInlineComment: PropTypes.func.isRequired,
    updateInlineComment: PropTypes.func.isRequired,
    getRecentComments: PropTypes.func.isRequired,
    handleAvatarUpload: PropTypes.func.isRequired,
    handleMarkAllAsRead: PropTypes.func.isRequired,
    handleNotificationClick: PropTypes.func.isRequired,
    closeCommentModal: PropTypes.func.isRequired,
    handleNewComment: PropTypes.func.isRequired,
    updateNewComment: PropTypes.func.isRequired
  }).isRequired
};

export default SafeSpaceView;