const axios = require('axios');

// Comprehensive test untuk fitur anonymous posting
async function comprehensiveTest() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('🧪 COMPREHENSIVE ANONYMOUS POSTING TEST\n');
  console.log('=====================================\n');
  
  try {
    // Step 1: Login
    console.log('1. 🔐 Authentication Test...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    console.log('✅ Login successful');
    console.log(`   User: ${loginResponse.data.user.name}`);
    
    // Step 2: Test berbagai variasi anonymous flag
    console.log('\n2. 🧪 Testing Various Anonymous Flag Formats...');
    
    const testCases = [
      { is_anonymous: true, expected: true, description: 'Boolean true' },
      { is_anonymous: false, expected: false, description: 'Boolean false' },
      { is_anonymous: 'true', expected: true, description: 'String "true"' },
      { is_anonymous: 'false', expected: false, description: 'String "false"' },
      { is_anonymous: 1, expected: true, description: 'Number 1' },
      { is_anonymous: 0, expected: false, description: 'Number 0' },
    ];
    
    const results = [];
    
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`   Testing ${testCase.description}...`);
      
      try {
        const response = await axios.post(`${baseURL}/safespace/posts`, {
          content: `Test post ${i + 1}: ${testCase.description}`,
          is_anonymous: testCase.is_anonymous
        }, { headers });
        
        const isCorrect = response.data.isAnonymous === testCase.expected;
        const authorName = response.data.author.name;
        const hasAvatar = response.data.author.avatar !== null;
        
        results.push({
          testCase: testCase.description,
          input: testCase.is_anonymous,
          expected: testCase.expected,
          actual: response.data.isAnonymous,
          authorName,
          hasAvatar,
          correct: isCorrect
        });
        
        console.log(`   ${isCorrect ? '✅' : '❌'} ${testCase.description}: ${response.data.isAnonymous} (author: ${authorName})`);
        
      } catch (error) {
        console.log(`   ❌ ${testCase.description}: ERROR - ${error.message}`);
        results.push({
          testCase: testCase.description,
          error: error.message,
          correct: false
        });
      }
    }
    
    // Step 3: Test frontend compatibility
    console.log('\n3. 🌐 Frontend Compatibility Test...');
    
    // Test dengan format yang dikirim frontend
    const frontendTest = await axios.post(`${baseURL}/safespace/posts`, {
      content: 'Frontend compatibility test - anonymous',
      is_anonymous: true  // Format yang dikirim dari frontend
    }, { headers });
    
    console.log('✅ Frontend format test:');
    console.log(`   Author: ${frontendTest.data.author.name}`);
    console.log(`   Is Anonymous: ${frontendTest.data.isAnonymous}`);
    console.log(`   Avatar: ${frontendTest.data.author.avatar}`);
    
    // Step 4: Test retrieval functions
    console.log('\n4. 📋 Post Retrieval Test...');
    
    const allPosts = await axios.get(`${baseURL}/safespace/posts`, { headers });
    const anonymousPosts = allPosts.data.filter(post => post.isAnonymous);
    const normalPosts = allPosts.data.filter(post => !post.isAnonymous);
    
    console.log(`✅ Retrieved ${allPosts.data.length} total posts`);
    console.log(`   Anonymous posts: ${anonymousPosts.length}`);
    console.log(`   Normal posts: ${normalPosts.length}`);
    
    // Verify anonymous posts don't show real names
    const anonymousWithRealNames = anonymousPosts.filter(post => 
      post.author.name !== 'Anonim' && post.author.name !== 'Anonymous'
    );
    
    if (anonymousWithRealNames.length > 0) {
      console.log('❌ Found anonymous posts with real names:');
      anonymousWithRealNames.forEach(post => {
        console.log(`   Post ${post.id}: "${post.author.name}"`);
      });
    } else {
      console.log('✅ All anonymous posts properly hide author names');
    }
    
    // Step 5: Test specific post retrieval
    console.log('\n5. 🔍 Individual Post Retrieval Test...');
    
    if (anonymousPosts.length > 0) {
      const testPost = anonymousPosts[0];
      const individualPost = await axios.get(`${baseURL}/safespace/posts/${testPost.id}`, { headers });
      
      console.log('✅ Individual anonymous post retrieval:');
      console.log(`   Author: ${individualPost.data.author.name}`);
      console.log(`   Is Anonymous: ${individualPost.data.isAnonymous}`);
      console.log(`   Avatar: ${individualPost.data.author.avatar}`);
    }
    
    // Step 6: Summary
    console.log('\n6. 📊 TEST SUMMARY');
    console.log('==================');
    
    const passedTests = results.filter(r => r.correct).length;
    const totalTests = results.length;
    
    console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED! Anonymous posting feature is fully functional.');
    } else {
      console.log('\n⚠️ Some tests failed. Details:');
      results.filter(r => !r.correct).forEach(result => {
        console.log(`   ❌ ${result.testCase}: Expected ${result.expected}, got ${result.actual}`);
      });
    }
    
    // Step 7: Database verification
    console.log('\n7. 🗄️ Database Verification...');
    console.log('Check database directly to ensure is_anonymous field is properly set.');
    
  } catch (error) {
    console.error('❌ Comprehensive test failed:', error.response?.data || error.message);
  }
}

// Run comprehensive test
comprehensiveTest();
