const axios = require('axios');

// Performance test untuk mengukur response time dan caching
async function performanceTest() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('🚀 PERFORMANCE OPTIMIZATION TEST\n');
  console.log('=================================\n');
  
  try {
    // Step 1: Login
    console.log('1. 🔐 Authentication...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    console.log('✅ Login successful\n');
    
    // Step 2: Test initial load time
    console.log('2. ⏱️ Testing Initial Load Performance...');
    
    const startTime1 = Date.now();
    const firstLoad = await axios.get(`${baseURL}/safespace/posts`, { headers });
    const firstLoadTime = Date.now() - startTime1;
    
    console.log(`✅ First load: ${firstLoadTime}ms (${firstLoad.data.length} posts)`);
    
    // Step 3: Test cached load time
    console.log('\n3. 🗄️ Testing Cache Performance...');
    
    const startTime2 = Date.now();
    const secondLoad = await axios.get(`${baseURL}/safespace/posts`, { headers });
    const secondLoadTime = Date.now() - startTime2;
    
    console.log(`✅ Cached load: ${secondLoadTime}ms (${secondLoad.data.length} posts)`);
    
    const cacheImprovement = ((firstLoadTime - secondLoadTime) / firstLoadTime * 100).toFixed(1);
    console.log(`📈 Cache improvement: ${cacheImprovement}% faster`);
    
    // Step 4: Test multiple concurrent requests
    console.log('\n4. 🔄 Testing Concurrent Requests...');
    
    const concurrentStart = Date.now();
    const concurrentPromises = Array(5).fill().map(() => 
      axios.get(`${baseURL}/safespace/posts`, { headers })
    );
    
    const concurrentResults = await Promise.all(concurrentPromises);
    const concurrentTime = Date.now() - concurrentStart;
    
    console.log(`✅ 5 concurrent requests: ${concurrentTime}ms total`);
    console.log(`📊 Average per request: ${(concurrentTime / 5).toFixed(1)}ms`);
    
    // Step 5: Test cache invalidation
    console.log('\n5. 🔄 Testing Cache Invalidation...');
    
    // Create a new post to invalidate cache
    const newPostStart = Date.now();
    await axios.post(`${baseURL}/safespace/posts`, {
      content: 'Performance test post - cache invalidation test',
      is_anonymous: false
    }, { headers });
    const newPostTime = Date.now() - newPostStart;
    
    console.log(`✅ New post created: ${newPostTime}ms`);
    
    // Test if cache was properly invalidated
    const afterPostStart = Date.now();
    const afterPostLoad = await axios.get(`${baseURL}/safespace/posts`, { headers });
    const afterPostTime = Date.now() - afterPostStart;
    
    console.log(`✅ Load after new post: ${afterPostTime}ms (${afterPostLoad.data.length} posts)`);
    
    // Step 6: Test user data caching
    console.log('\n6. 👤 Testing User Data Performance...');
    
    const userStart1 = Date.now();
    await axios.get(`${baseURL}/auth/me`, { headers });
    const userTime1 = Date.now() - userStart1;
    
    const userStart2 = Date.now();
    await axios.get(`${baseURL}/auth/me`, { headers });
    const userTime2 = Date.now() - userStart2;
    
    console.log(`✅ User data first load: ${userTime1}ms`);
    console.log(`✅ User data second load: ${userTime2}ms`);
    
    // Step 7: Performance Summary
    console.log('\n7. 📊 PERFORMANCE SUMMARY');
    console.log('========================');
    
    const results = {
      'Initial Load': `${firstLoadTime}ms`,
      'Cached Load': `${secondLoadTime}ms`,
      'Cache Improvement': `${cacheImprovement}%`,
      'Concurrent Avg': `${(concurrentTime / 5).toFixed(1)}ms`,
      'Post Creation': `${newPostTime}ms`,
      'After Cache Clear': `${afterPostTime}ms`
    };
    
    Object.entries(results).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    // Performance benchmarks
    console.log('\n🎯 PERFORMANCE BENCHMARKS:');
    console.log('==========================');
    
    const benchmarks = [
      { metric: 'Initial Load', value: firstLoadTime, target: 1000, unit: 'ms' },
      { metric: 'Cached Load', value: secondLoadTime, target: 200, unit: 'ms' },
      { metric: 'Cache Improvement', value: parseFloat(cacheImprovement), target: 50, unit: '%' },
      { metric: 'Concurrent Avg', value: concurrentTime / 5, target: 300, unit: 'ms' }
    ];
    
    let passedBenchmarks = 0;
    benchmarks.forEach(benchmark => {
      const passed = benchmark.unit === '%' 
        ? benchmark.value >= benchmark.target 
        : benchmark.value <= benchmark.target;
      
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${benchmark.metric}: ${benchmark.value}${benchmark.unit} (target: ${benchmark.target}${benchmark.unit}) ${status}`);
      
      if (passed) passedBenchmarks++;
    });
    
    console.log(`\n📈 Overall Performance: ${passedBenchmarks}/${benchmarks.length} benchmarks passed`);
    
    if (passedBenchmarks === benchmarks.length) {
      console.log('🎉 EXCELLENT! All performance benchmarks passed.');
    } else if (passedBenchmarks >= benchmarks.length * 0.75) {
      console.log('👍 GOOD! Most performance benchmarks passed.');
    } else {
      console.log('⚠️ NEEDS IMPROVEMENT! Several benchmarks failed.');
    }
    
    // Step 8: Recommendations
    console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
    console.log('=================================');
    
    if (firstLoadTime > 1000) {
      console.log('   • Consider database query optimization');
      console.log('   • Add database indexing for frequently queried fields');
    }
    
    if (secondLoadTime > 200) {
      console.log('   • Improve caching strategy');
      console.log('   • Consider using Redis for better cache performance');
    }
    
    if (parseFloat(cacheImprovement) < 50) {
      console.log('   • Cache hit rate could be improved');
      console.log('   • Consider longer cache TTL for static data');
    }
    
    if (concurrentTime / 5 > 300) {
      console.log('   • Server may need better concurrency handling');
      console.log('   • Consider connection pooling optimization');
    }
    
    console.log('\n✅ Performance test completed!');
    
  } catch (error) {
    console.error('❌ Performance test failed:', error.response?.data || error.message);
  }
}

// Run performance test
performanceTest();
