<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" content="#ffffff" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vite + React PWA Boilerplate</title>
  <meta name="description" content="React + Vite PWA Boilerplate" />
  <meta name="author" content="Salman Dabbakuti" />
  <meta name="keywords" content="React, Vite, React Vite, React Boilerplate" />
  <link rel="manifest" href="/manifest.webmanifest" type="application/manifest+json" />
  <link rel="icon" href="/favicon.ico" type="image/svg+xml" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon-180x180.png" />
  <link rel="mask-icon" href="/maskable-icon-512x512.png" color="#FFFFFF" />
  <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
  <meta name="theme-color" media="(prefers-color-scheme: dark)" content="black">
  <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
</body>

</html>