const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Test configuration
const BASE_URL = 'http://localhost:5000/api';

// You'll need to replace this with a valid JWT token
const TEST_TOKEN = 'YOUR_JWT_TOKEN_HERE';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`
  }
});

async function testAvatarUpload() {
  try {
    console.log('🧪 Testing Avatar Upload System...\n');

    // Test 1: Check current user data
    console.log('1️⃣ Checking current user data');
    
    try {
      const userResponse = await api.get('/auth/me');
      console.log('✅ Current user data retrieved');
      console.log(`📝 Current avatar: ${userResponse.data.avatar || 'No avatar set'}`);
      console.log(`📝 Cloudinary ID: ${userResponse.data.cloudinary_public_id || 'No Cloudinary ID'}`);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('⚠️ User data test skipped (need valid JWT token)');
      } else {
        console.log('❌ User data error:', error.response?.data?.message || error.message);
      }
    }

    // Test 2: Test avatar upload endpoint structure
    console.log('\n2️⃣ Testing avatar upload endpoint');
    
    try {
      const formData = new FormData();
      // Don't add file to test validation
      
      const uploadResponse = await api.post('/upload/avatar', formData, {
        headers: {
          ...formData.getHeaders()
        }
      });
      
      console.log('✅ Upload endpoint accessible');
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('No file uploaded')) {
        console.log('✅ Upload validation working (correctly rejects empty uploads)');
      } else if (error.response?.status === 401) {
        console.log('⚠️ Upload test skipped (need valid JWT token)');
      } else {
        console.log('❌ Upload endpoint error:', error.response?.data?.message || error.message);
      }
    }

    // Test 3: Test profile stats endpoint
    console.log('\n3️⃣ Testing profile stats endpoint');
    
    try {
      const statsResponse = await api.get('/upload/profile-stats');
      console.log('✅ Profile stats endpoint working');
      console.log(`📊 User stats:`, statsResponse.data.stats);
      console.log(`👤 User info:`, {
        id: statsResponse.data.user.id,
        name: statsResponse.data.user.name,
        avatar: statsResponse.data.user.avatar || 'No avatar'
      });
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('⚠️ Profile stats test skipped (need valid JWT token)');
      } else {
        console.log('❌ Profile stats error:', error.response?.data?.message || error.message);
      }
    }

    console.log('\n📋 Avatar Upload System Status:');
    console.log('  ✅ Database schema updated with cloudinary_public_id column');
    console.log('  ✅ User model updated to handle avatar and cloudinary_public_id');
    console.log('  ✅ Upload controller uses User.update() method');
    console.log('  ✅ Cloudinary integration configured');
    console.log('  ✅ File validation and error handling implemented');
    
    console.log('\n🔧 Frontend Integration:');
    console.log('  const uploadAvatar = async (file) => {');
    console.log('    const formData = new FormData();');
    console.log('    formData.append("avatar", file);');
    console.log('    ');
    console.log('    const response = await api.post("/api/upload/avatar", formData, {');
    console.log('      headers: { "Content-Type": "multipart/form-data" }');
    console.log('    });');
    console.log('    ');
    console.log('    // Update user state with new avatar');
    console.log('    setUser(response.data.user);');
    console.log('    ');
    console.log('    return response.data;');
    console.log('  };');
    
    console.log('\n📤 Expected Response Format:');
    console.log('  {');
    console.log('    "message": "Avatar uploaded successfully",');
    console.log('    "user": {');
    console.log('      "id": 1,');
    console.log('      "name": "User Name",');
    console.log('      "email": "<EMAIL>",');
    console.log('      "avatar": "https://res.cloudinary.com/dzrd37naa/image/upload/...",');
    console.log('      "role": "user",');
    console.log('      "cloudinary_public_id": "pulih-hati/avatars/user_1_timestamp"');
    console.log('    },');
    console.log('    "avatar": {');
    console.log('      "small": "optimized_small_url",');
    console.log('      "medium": "optimized_medium_url",');
    console.log('      "large": "optimized_large_url",');
    console.log('      "original": "original_cloudinary_url"');
    console.log('    }');
    console.log('  }');

    console.log('\n🎯 Key Fixes Applied:');
    console.log('  🔧 Fixed User model database connection (pool → query)');
    console.log('  🔧 Fixed database schema (pulihhati → "pulihHati")');
    console.log('  🔧 Added cloudinary_public_id support in User.update()');
    console.log('  🔧 Updated uploadController to use User.update()');
    console.log('  🔧 Ensured avatar URL is saved to database');

    console.log('\n🎉 Avatar upload system ready for testing with real image files!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run tests
testAvatarUpload();
