// Test script untuk memverifikasi frontend anonymous posting
// Buka browser console dan jalankan script ini

async function testFrontendAnonymous() {
  console.log('🧪 Testing Frontend Anonymous Posting...\n');
  
  // Simulasi login (asumsi sudah login)
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('❌ No token found. Please login first.');
    return;
  }
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // Test 1: Post normal (tidak anonymous)
    console.log('1. 📝 Testing normal post from frontend...');
    const normalResponse = await fetch(`${baseURL}/safespace/posts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'Frontend test - normal post',
        is_anonymous: false
      })
    });
    
    const normalPost = await normalResponse.json();
    console.log('✅ Normal post:', normalPost);
    
    // Test 2: Post anonymous
    console.log('\n2. 🕶️ Testing anonymous post from frontend...');
    const anonymousResponse = await fetch(`${baseURL}/safespace/posts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'Frontend test - anonymous post',
        is_anonymous: true
      })
    });
    
    const anonymousPost = await anonymousResponse.json();
    console.log('✅ Anonymous post:', anonymousPost);
    
    // Verification
    console.log('\n🎯 VERIFICATION:');
    console.log(`Normal post author: ${normalPost.author.name} (should not be "Anonim")`);
    console.log(`Anonymous post author: ${anonymousPost.author.name} (should be "Anonim")`);
    console.log(`Anonymous post avatar: ${anonymousPost.author.avatar} (should be null)`);
    
    const success = normalPost.author.name !== 'Anonim' && 
                   anonymousPost.author.name === 'Anonim' && 
                   anonymousPost.author.avatar === null;
    
    console.log(success ? '\n🎉 Frontend test PASSED!' : '\n❌ Frontend test FAILED!');
    
  } catch (error) {
    console.error('❌ Frontend test error:', error);
  }
}

// Untuk menjalankan di browser console:
// testFrontendAnonymous();

console.log('📋 Frontend test script loaded. Run testFrontendAnonymous() in browser console after login.');
