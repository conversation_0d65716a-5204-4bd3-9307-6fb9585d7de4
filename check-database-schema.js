const { Pool } = require('./PulihHati-Backend/node_modules/pg');

// Database configuration
const pool = new Pool({
  user: '<PERSON><PERSON><PERSON>Hati_yousource',
  host: '8tqyk.h.filess.io',
  database: 'PulihHati_yousource',
  password: 'f245c22ee61374fe0af41af38844b450cf223e74',
  port: 5433,
  ssl: false
});

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema...\n');
  
  try {
    // Check posts table structure
    console.log('1. 📋 Posts table structure:');
    const postsSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'pulihHati' AND table_name = 'posts'
      ORDER BY ordinal_position;
    `);
    
    console.table(postsSchema.rows);
    
    // Check if is_anonymous column exists
    const hasAnonymousColumn = postsSchema.rows.some(row => row.column_name === 'is_anonymous');
    console.log(`\n✅ is_anonymous column exists: ${hasAnonymousColumn}`);
    
    if (!hasAnonymousColumn) {
      console.log('\n⚠️ Adding is_anonymous column...');
      await pool.query(`
        ALTER TABLE "pulihHati".posts 
        ADD COLUMN is_anonymous BOOLEAN DEFAULT FALSE;
      `);
      console.log('✅ is_anonymous column added successfully!');
    }
    
    // Check recent posts with anonymous flag
    console.log('\n2. 📝 Recent posts with anonymous status:');
    const recentPosts = await pool.query(`
      SELECT id, content, author_id, is_anonymous, created_at
      FROM "pulihHati".posts 
      ORDER BY created_at DESC 
      LIMIT 5;
    `);
    
    console.table(recentPosts.rows);
    
    // Check if there are any anonymous posts
    const anonymousCount = await pool.query(`
      SELECT COUNT(*) as count 
      FROM "pulihHati".posts 
      WHERE is_anonymous = true;
    `);
    
    console.log(`\n📊 Anonymous posts count: ${anonymousCount.rows[0].count}`);
    
    // Test query that backend uses
    console.log('\n3. 🧪 Testing backend query format:');
    const testQuery = await pool.query(`
      SELECT
        p.*,
        u.name as author_name,
        u.avatar as author_avatar
      FROM "pulihHati".posts p
      LEFT JOIN "pulihHati".users u ON p.author_id = u.id
      WHERE p.is_anonymous IS NOT NULL
      ORDER BY p.created_at DESC
      LIMIT 3;
    `);
    
    console.table(testQuery.rows);
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await pool.end();
  }
}

checkDatabaseSchema();
