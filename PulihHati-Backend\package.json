{"name": "pulih-hati-backend", "version": "1.0.0", "description": "Backend API for PulihHati mental health application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.6.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^2.0.0", "pg": "^8.16.0", "redis": "^4.6.13", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.4", "nodemon": "^3.1.10", "supertest": "^6.3.3"}}