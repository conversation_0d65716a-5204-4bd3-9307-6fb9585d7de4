{"name": "react-vite-boilerplate", "description": "A React boilerplate with Vite, ESLint, PWA, and more.", "version": "0.0.1", "private": true, "type": "module", "license": "MIT", "author": "PulihHati", "scripts": {"dev": "vite --port 3000", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "generate-pwa-assets": "pwa-assets-generator --preset minimal public/vite.svg"}, "dependencies": {"axios": "^1.9.0", "lucide-react": "^0.294.0", "path": "^0.12.7", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-player": "^2.16.0", "react-redux": "^9.0.4", "react-router-dom": "^7.6.0", "styled-components": "^6.1.18"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vite-pwa/assets-generator": "^0.0.10", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.4.5", "vite-plugin-pwa": "^0.16.6"}}