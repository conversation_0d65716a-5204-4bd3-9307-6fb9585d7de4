{"level":"error","message":"Error: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","service":"pulih-hati-backend","timestamp":"2025-05-24 20:26:09"}
{"level":"error","message":"Error creating tables: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-24 21:34:27"}
{"level":"error","message":"PostgreSQL Connection Error: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-24 21:38:47"}
{"level":"error","message":"Error creating tables: permission denied for schema public","service":"pulih-hati-backend","timestamp":"2025-05-24 21:39:54"}
{"level":"error","message":"Error creating tables: permission denied for schema public","service":"pulih-hati-backend","timestamp":"2025-05-24 21:40:18"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:37:51"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:38:15"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:40:09"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:40:10"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:41:53"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:41:55"}
{"level":"error","message":"TypeError: User.matchPassword is not a function\n    at exports.login (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 12:42:16"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:04:31"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:04:32"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:04:33"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:04:34"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:04:57"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:04:59"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:43"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:51"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:54"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:54"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:55"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:55"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:55"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:56"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:57"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:05:57"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:21:32"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:21:33"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:21:44"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:21:52"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:22:30"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:22:31"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:22:36"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:22:36"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:22:46"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:22:50"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:24:42"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:24:43"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:24:49"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:24:50"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:24:54"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:25:00"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:10:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:25:00"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:8:20)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:45:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:25:15"}
{"level":"error","message":"Error connecting to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 13:37:50"}
{"level":"error","message":"Error connecting to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 13:38:08"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:39:59"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:40:00"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:40:07"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:40:15"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:220:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:40:16"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:40:17"}
{"level":"error","message":"Error connecting to database: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 13:42:20"}
{"level":"error","message":"Error connecting to database: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 13:42:24"}
{"level":"error","message":"Error connecting to database: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 13:42:34"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:43:52"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:43:53"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:45:13"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:20:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:11:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:45:14"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:172:25)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:66:26)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:52:19"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:172:25)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:66:26)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:52:23"}
{"level":"error","message":"error: relation \"pulihHati.likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:172:25)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:66:26)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:53:10"}
{"level":"error","message":"All create queries failed","service":"pulih-hati-backend","timestamp":"2025-05-25 13:57:22"}
{"level":"error","message":"Error in Post.create: permission denied for schema public","service":"pulih-hati-backend","timestamp":"2025-05-25 13:57:22"}
{"level":"error","message":"Error in createPost: permission denied for schema public","service":"pulih-hati-backend","timestamp":"2025-05-25 13:57:22"}
{"level":"error","message":"error: permission denied for schema public\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:43:22)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:65:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 13:57:22"}
{"level":"error","message":"All create queries failed","service":"pulih-hati-backend","timestamp":"2025-05-25 14:01:31"}
{"level":"error","message":"Error in Post.create: permission denied for schema public","service":"pulih-hati-backend","timestamp":"2025-05-25 14:01:31"}
{"level":"error","message":"Error in createPost: permission denied for schema public","service":"pulih-hati-backend","timestamp":"2025-05-25 14:01:31"}
{"level":"error","message":"Error creating posts table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:17"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:17"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:17"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:26"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:27"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:51"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:05:52"}
{"level":"error","message":"Error creating posts table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:44"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:44"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:44"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:45"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:45"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:52"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:07:52"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:278:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:09"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:15"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:15"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:20"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:20"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:25"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:25"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:26"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:26"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:36"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:36"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:37"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:37"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:45"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:45"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:47"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:47"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:48"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:48"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:49"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:49"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:278:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:51"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:55"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:55"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:56"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:10:56"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:20"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:160:9)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\app.js:24:5","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:20"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:21"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:192:9)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\app.js:24:5","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:21"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:29"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:29"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:278:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:12:38"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:03"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:160:9)\n    at async initializeDatabase (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:15:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:03"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:03"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:160:9)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\app.js:24:5","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:03"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:04"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:192:9)\n    at async initializeDatabase (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:15:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:04"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:04"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:192:9)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\app.js:24:5","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:04"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:07"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:07"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:08"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:08"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:12"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:12"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:12"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:12"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:29"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:160:9)\n    at async initializeDatabase (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:15:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:29"}
{"level":"error","message":"Error creating likes table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:29"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:160:9)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\app.js:27:5","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:29"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:30"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:192:9)\n    at async initializeDatabase (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:15:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:30"}
{"level":"error","message":"Error creating comments table: no schema has been selected to create in","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:30"}
{"level":"error","message":"Stack trace: error: no schema has been selected to create in\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ensureTablesExist (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\utils\\db-checker.js:192:9)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\app.js:27:5","service":"pulih-hati-backend","timestamp":"2025-05-25 14:13:30"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:15:36"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:15:36"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:278:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:15:40"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:278:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:15:45"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:16:19"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:16:19"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:16:26"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:16:26"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:16:26"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:16:26"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:17:25"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:17:25"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:17:26"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:17:26"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:19:38"}
{"level":"error","message":"TypeError: Post.getBookmarkedPosts is not a function\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:279:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:02"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:15"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:18"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:19"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:20"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:20"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:20:20"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:23:41"}
{"level":"error","message":"Error checking/creating bookmarks table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:24:09"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:24:12"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:24:16"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:24:18"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:24:21"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:24:21"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:25:28"}
{"level":"error","message":"Error checking/creating posts table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:25:28"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:06"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:06"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.bookmarkPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:248:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:21"}
{"level":"error","message":"Error checking/creating bookmarks table: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:27"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:29"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:29"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:34"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:34"}
{"level":"error","message":"Error in Post.findAll: relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:35"}
{"level":"error","message":"Stack trace: error: relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:37:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:26:35"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:42:20"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:42:22"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:42:24"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:42:27"}
{"level":"error","message":"TypeError: Post.findById is not a function\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:198:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:45:03"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.post_likes\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:20"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.post_likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:128:27)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:204:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:20"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.post_likes\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:22"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.post_likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:128:27)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:204:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:22"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.post_likes\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:23"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.post_likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:128:27)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:204:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:23"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.post_likes\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:23"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.post_likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:128:27)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:204:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:49:23"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.post_likes\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 14:50:51"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.post_likes\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:128:27)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:204:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:50:51"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:32"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:32"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:43"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:43"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:43"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:43"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:57"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:54:57"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:01"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:01"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:09"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:09"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:11"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:20"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:20"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:20"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:20"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:33"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:55:33"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:56:33"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:56:33"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:56:34"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:56:34"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:57:19"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:57:19"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:57:20"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:57:20"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:58:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:58:11"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 14:58:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 14:58:11"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:01:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:01:11"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:01:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:01:11"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:00"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:00"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:03"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:03"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:09"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:09"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:17"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:17"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:18"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:18"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:22"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:22"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:22"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:22"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:25"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:25"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:25"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:25"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:26"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:26"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:27"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:03:27"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:07:58"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:07:58"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:07:58"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:07:58"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:46"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:46"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:46"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:47"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:48"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:48"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:57"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:57"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:57"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:57"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:59"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:59"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:59"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:08:59"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:09:36"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:09:36"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:09:36"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:09:36"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:39"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:39"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:39"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:39"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:48"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:48"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:49"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:49"}
{"level":"error","message":"Error in Post.getBookmarkedPosts: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:51"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:69:33)\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:406:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:51"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:52"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:52"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:56"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:13:56"}
{"level":"error","message":"Error in Post.create: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:14:05"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:11:33)\n    at exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:129:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:14:05"}
{"level":"error","message":"Error in createPost: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:14:05"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:07"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:07"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:21"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:21"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:33"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:33"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:33"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:33"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:34"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:34"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:38"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:16:38"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:03"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:03"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:04"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:04"}
{"level":"error","message":"Error in Post.getBookmarkedPosts: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:06"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:69:33)\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:406:40)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:06"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:08"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:17:08"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:19:00"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:19:00"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:19:00"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:19:00"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:07"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:07"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:08"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:08"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:11"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:11"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:11"}
{"level":"error","message":"Error in Post.create: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:31"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:11:33)\n    at exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:122:29)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:31"}
{"level":"error","message":"Error in createPost: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:31"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:44"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:44"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:44"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:44"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:54"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:54"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:54"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:54"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:59"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:59"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:59"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:22:59"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:23:04"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:23:04"}
{"level":"error","message":"Error in Post.findAll: pool.query is not a function","service":"pulih-hati-backend","timestamp":"2025-05-25 15:23:04"}
{"level":"error","message":"Stack trace: TypeError: pool.query is not a function\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:33)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:15:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:23:04"}
{"level":"error","message":"Error in Post.findAll: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:28:50"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:56:32)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:28:50"}
{"level":"error","message":"Error in Post.findAll: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:28:50"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:56:32)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:28:50"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:39:57"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:162:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:39:57"}
{"level":"error","message":"Error adding comment: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:39:57"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:162:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:39:57"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:42:50"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:164:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:42:50"}
{"level":"error","message":"Error in findById or addComment: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:42:50"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:164:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:42:50"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:34"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:164:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:34"}
{"level":"error","message":"Error in findById or addComment: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:34"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:164:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:34"}
{"level":"error","message":"Error in Post.findById: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:40"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:164:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:40"}
{"level":"error","message":"Error in findById or addComment: relation \"pulihHati.comments\" does not exist","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:40"}
{"level":"error","message":"Stack trace: error: relation \"pulihHati.comments\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:150:30)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:164:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:46:40"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:50:04"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:50:07"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:50:08"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:50:08"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:50:08"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:53:39"}
{"level":"error","message":"Stack trace: ReferenceError: pool is not defined\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:167:33)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:53:39"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:53:41"}
{"level":"error","message":"Stack trace: ReferenceError: pool is not defined\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:167:33)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:53:41"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:53:42"}
{"level":"error","message":"Stack trace: ReferenceError: pool is not defined\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:167:33)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:53:42"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 15:55:16"}
{"level":"error","message":"Stack trace: ReferenceError: pool is not defined\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:167:33)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 15:55:16"}
{"level":"error","message":"Error checking/creating comments table: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 16:04:56"}
{"level":"error","message":"Stack trace: ReferenceError: pool is not defined\n    at exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:167:33)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:04:56"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:26"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 7)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:26"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:27"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 7)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:27"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:30"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 4)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:30"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:31"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:31"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:59"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:10:59"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:11:00"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 9)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:11:00"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:11:36"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 8)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:11:36"}
{"level":"error","message":"Error in Post.findAll: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:11:38"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:55:32\n    at async Promise.all (index 3)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:53:21)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:12:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:11:38"}
{"level":"error","message":"Error fetching posts: getClient is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 16:16:41"}
{"level":"error","message":"Stack trace: ReferenceError: getClient is not defined\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:16:41"}
{"level":"error","message":"Error fetching posts: getClient is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 16:16:41"}
{"level":"error","message":"Stack trace: ReferenceError: getClient is not defined\n    at Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:30)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:16:41"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:19:30"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:19:30"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:19:31"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:19:31"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:25:42"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:25:42"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:25:42"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:25:42"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:26:52"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:26:52"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:26:53"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:26:53"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:00"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:00"}
{"level":"error","message":"Error fetching posts: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:01"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:20:18)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:29:20)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:01"}
{"level":"error","message":"Error in Post.findAll: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:51"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:51"}
{"level":"error","message":"Error in Post.findAll: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:52"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:33:22)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:27:52"}
{"level":"error","message":"Error in Post.findAll: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:29:23"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:34:27)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:29:23"}
{"level":"error","message":"Error in Post.findAll: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:29:24"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:34:27)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:13:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:29:24"}
{"level":"error","message":"Error checking/creating posts table: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:58"}
{"level":"error","message":"Error checking posts table: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:58"}
{"level":"error","message":"Error in Post.findAll: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:58"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:60:27)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:29:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:58"}
{"level":"error","message":"Error checking/creating posts table: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:59"}
{"level":"error","message":"Error checking posts table: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:59"}
{"level":"error","message":"Error in Post.findAll: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:59"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:60:27)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:29:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:30:59"}
{"level":"error","message":"Error checking/creating posts table: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:31:50"}
{"level":"error","message":"Error checking/creating posts table: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-25 16:31:51"}
{"level":"error","message":"Error checking/creating posts table: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:32:45"}
{"level":"error","message":"Error checking/creating posts table: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:32:45"}
{"level":"error","message":"Error checking/creating posts table: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 16:33:59"}
{"level":"error","message":"Error in Post.findAll: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 16:34:00"}
{"level":"error","message":"Stack trace: Error: The server does not support SSL connections\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:35:29)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:29:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:34:00"}
{"level":"error","message":"Error checking/creating posts table: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 16:34:00"}
{"level":"error","message":"Error in Post.findAll: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 16:34:00"}
{"level":"error","message":"Stack trace: Error: The server does not support SSL connections\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Post.findAll (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\Post.js:35:29)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:29:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:34:00"}
{"level":"error","message":"Error in addComment controller: duplicate key value violates unique constraint \"post_comments_pkey\"","service":"pulih-hati-backend","timestamp":"2025-05-25 16:35:04"}
{"level":"error","message":"Stack trace: error: duplicate key value violates unique constraint \"post_comments_pkey\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.addComment (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:267:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 16:35:04"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:44"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:44"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:44"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:45"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:45"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:46"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:46"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:47"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:47"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:48"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:48"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:49"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:50"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:50"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:51"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:51"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:52"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:52"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:53"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:53"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:54"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:54"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:55"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:55"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:56"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:56"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:57"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:57"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:58"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:58"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:59"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:19:59"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:00"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:00"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:01"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:01"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:02"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:02"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:03"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:03"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:04"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:04"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:05"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:05"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:06"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:06"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:07"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:07"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:08"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:08"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:09"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:09"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:10"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:11"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:11"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:12"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:12"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:13"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:13"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:14"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:14"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:15"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:15"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:16"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:16"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:17"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:17"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:18"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:18"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:19"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:19"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:20"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:20"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:21"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:21"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:22"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:22"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:23"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:23"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:24"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:24"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:25"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:25"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:26"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:26"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:27"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:27"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:28"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:29"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:29"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:30"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:30"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:31"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:31"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:32"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:32"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:33"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:33"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:34"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:34"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:35"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:35"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:36"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:36"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:37"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:37"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:38"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:38"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:39"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:39"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:40"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:40"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:41"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:41"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:42"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:42"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:43"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:44"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:44"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:45"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:45"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:46"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:46"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:47"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:47"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:48"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:49"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:49"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:50"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:50"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:51"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:51"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:52"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:52"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:53"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:53"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:54"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:54"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:55"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:55"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:56"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:56"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:57"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:57"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:58"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:58"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:59"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:20:59"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:00"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:00"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:01"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:01"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:02"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:02"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:03"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:03"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:04"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:04"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:05"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:05"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:06"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:06"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:07"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:08"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:08"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:09"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:09"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:10"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:10"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:11"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:11"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:12"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:12"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:13"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:13"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:14"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:14"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:15"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:15"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:16"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:16"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:17"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:17"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:18"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:18"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:19"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:19"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:20"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:20"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:21"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:21"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:22"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:22"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:23"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:23"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:24"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:24"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:25"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:25"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:26"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:27"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:27"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:28"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:28"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:29"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:29"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:30"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:30"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:31"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:31"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:32"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:32"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:33"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:33"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:34"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:34"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:35"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:35"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:36"}
{"level":"error","message":"Redis Error: AggregateError","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:36"}
{"level":"error","message":"Redis connection error: ","service":"pulih-hati-backend","timestamp":"2025-05-25 20:21:40"}
{"level":"error","message":"Error fetching posts: total is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 20:29:10"}
{"level":"error","message":"Stack trace: ReferenceError: total is not defined\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:114:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:29:10"}
{"level":"error","message":"Error fetching posts: total is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 20:29:14"}
{"level":"error","message":"Stack trace: ReferenceError: total is not defined\n    at exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:114:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:29:14"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:31:59"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:03"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:25"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:25"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:25"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:35"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:35"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:35"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:47"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:48"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:48"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:52"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:33:18)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:28:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:52"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:52"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:33:18)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:28:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:32:52"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:02"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:02"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:02"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:04"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:33:18)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:28:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:04"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:04"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:33:18)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:28:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:04"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:10"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:11"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:33:11"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:38:47"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:38:48"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:38:48"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:39:49"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:33:18)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:18:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:39:49"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:39:50"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getClient (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:33:18)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:18:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:39:50"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:24"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:24"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:24"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:39"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:40"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:40"}
{"level":"error","message":"Error creating schema/tables: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:41"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:42"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:84:23)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:42"}
{"level":"error","message":"Error creating schema/tables: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:42"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:42"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:84:23)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:42:42"}
{"level":"error","message":"Database connection error: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:00"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:00"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:00"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:25"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:25"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:26"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:26"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:48"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:48"}
{"level":"error","message":"Error fetching posts: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:48"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:45:48"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:47:14"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:47:14"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:47:43"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:47:43"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:47:47"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:47:47"}
{"level":"error","message":"Error initializing database: password authentication failed for user \"postgres\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:49:03"}
{"level":"error","message":"Stack trace: error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:49:03"}
{"level":"error","message":"Error initializing database: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 20:49:06"}
{"level":"error","message":"Stack trace: Error: The server does not support SSL connections\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async initDb (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\initDb.js:10:5)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:10:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:49:06"}
{"level":"error","message":"Server startup error: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 20:49:59"}
{"level":"error","message":"Error fetching posts: Connection terminated unexpectedly","service":"pulih-hati-backend","timestamp":"2025-05-25 20:53:03"}
{"level":"error","message":"Stack trace: Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:69:27)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:53:03"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:53:17"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:53:17"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:53:22"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:53:22"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:54"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:54"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:54"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:54"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:55"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:55"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:55"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:55"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:59"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:54:59"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:00"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:00"}
{"level":"error","message":"Error fetching posts: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:40"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:40"}
{"level":"error","message":"Error fetching posts: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:41"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:42"}
{"level":"error","message":"Error fetching posts: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:48"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:20:25)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:55:48"}
{"level":"error","message":"Error fetching posts: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:58:05"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async query (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:65:18)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:35:27\n    at async Promise.all (index 1)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:34:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:58:05"}
{"level":"error","message":"Error fetching posts: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 20:58:07"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async query (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:65:18)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:35:27\n    at async Promise.all (index 2)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:34:19)","service":"pulih-hati-backend","timestamp":"2025-05-25 20:58:07"}
{"level":"error","message":"Error fetching bookmarked posts: pool is not defined","service":"pulih-hati-backend","timestamp":"2025-05-25 21:03:20"}
{"level":"error","message":"Stack trace: ReferenceError: pool is not defined\n    at exports.getBookmarkedPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:490:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:41:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 21:03:20"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:39:45"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:39:45"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:39:47"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:39:47"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:39:54"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:39:54"}
{"level":"error","message":"Error in User.findById: connect ETIMEDOUT 149.102.156.43:5433","service":"pulih-hati-backend","timestamp":"2025-05-25 22:40:59"}
{"level":"error","message":"Stack trace: Error: connect ETIMEDOUT 149.102.156.43:5433\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:40:59"}
{"level":"error","message":"Error in User.findById: connect ETIMEDOUT 149.102.156.43:5433","service":"pulih-hati-backend","timestamp":"2025-05-25 22:40:59"}
{"level":"error","message":"Stack trace: Error: connect ETIMEDOUT 149.102.156.43:5433\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:40:59"}
{"level":"error","message":"Error in User.findById: Connection terminated unexpectedly","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:21"}
{"level":"error","message":"Stack trace: Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:21"}
{"level":"error","message":"Error in User.findById: Connection terminated unexpectedly","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:21"}
{"level":"error","message":"Stack trace: Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:27:18)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:21"}
{"level":"error","message":"Failed to connect to database for transaction: Connection terminated due to connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:27"}
{"level":"error","message":"Failed to connect to database for transaction: Connection terminated due to connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:31"}
{"level":"error","message":"Failed to connect to database for transaction: Connection terminated due to connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:38"}
{"level":"error","message":"Failed to connect to database: Connection terminated due to connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:42:55"}
{"level":"error","message":"Database query error (attempt 1/3): Connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:43:18"}
{"level":"error","message":"Database connection error: Connection timeout. Recreating pool...","service":"pulih-hati-backend","timestamp":"2025-05-25 22:43:18"}
{"level":"error","message":"Database query error (attempt 2/3): Connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:43:35"}
{"level":"error","message":"Database connection error: Connection timeout. Recreating pool...","service":"pulih-hati-backend","timestamp":"2025-05-25 22:43:35"}
{"level":"error","message":"Database query error (attempt 3/3): Connection timeout","service":"pulih-hati-backend","timestamp":"2025-05-25 22:43:54"}
{"level":"error","message":"Database connection error: Connection timeout. Recreating pool...","service":"pulih-hati-backend","timestamp":"2025-05-25 22:43:54"}
{"level":"error","message":"Database query error (attempt 1/3): The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:47:49"}
{"level":"error","message":"Database query error (attempt 2/3): The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:47:55"}
{"level":"error","message":"Database query error (attempt 3/3): The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:08"}
{"level":"error","message":"Server startup error: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:08"}
{"level":"error","message":"Stack trace: Error: The server does not support SSL connections\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:55:16)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:30:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:08"}
{"level":"error","message":"Database query error (attempt 1/3): The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:12"}
{"level":"error","message":"Database query error (attempt 2/3): The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:19"}
{"level":"error","message":"Database query error (attempt 3/3): The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:32"}
{"level":"error","message":"Server startup error: The server does not support SSL connections","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:32"}
{"level":"error","message":"Stack trace: Error: The server does not support SSL connections\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:55:16)\n    at async startServer (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\server.js:30:5)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:48:32"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:49:54"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:49:54"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:49:55"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:49:55"}
{"level":"error","message":"Transaction error (attempt 1/3): too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:02"}
{"level":"error","message":"Transaction error (attempt 2/3): too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:11"}
{"level":"error","message":"Transaction error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:33"}
{"level":"error","message":"Error fetching posts: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:33"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:155:16)\n    at async exports.getPosts (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:16:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:33"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:40"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:53:40"}
{"level":"error","message":"Error in User.findById: too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:54:43"}
{"level":"error","message":"Stack trace: error: too many connections for role \"PulihHati_yousource\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:54:43"}
{"level":"error","message":"Transaction error (attempt 1/3): too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:55:45"}
{"level":"error","message":"Transaction error (attempt 2/3): too many connections for role \"PulihHati_yousource\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:55:52"}
{"level":"error","message":"Transaction error (attempt 1/3): invalid input syntax for type integer: \"undefined\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:58:34"}
{"level":"error","message":"Transaction error (attempt 2/3): invalid input syntax for type integer: \"undefined\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:58:40"}
{"level":"error","message":"Transaction error (attempt 3/3): invalid input syntax for type integer: \"undefined\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:58:53"}
{"level":"error","message":"Error liking post: invalid input syntax for type integer: \"undefined\"","service":"pulih-hati-backend","timestamp":"2025-05-25 22:58:53"}
{"level":"error","message":"Stack trace: error: invalid input syntax for type integer: \"undefined\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:259:26\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.likePost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:257:20)","service":"pulih-hati-backend","timestamp":"2025-05-25 22:58:53"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-25 23:10:58"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-25 23:19:36"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-25 23:30:17"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:26"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Error in User.findById: getaddrinfo ENOTFOUND 8tqyk.h.filess.io","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Stack trace: Error: getaddrinfo ENOTFOUND 8tqyk.h.filess.io\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:28:41"}
{"level":"error","message":"Database query error (attempt 1/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 00:37:15"}
{"level":"error","message":"Database query error (attempt 2/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 00:37:21"}
{"level":"error","message":"Database query error (attempt 3/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 00:37:33"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-26 00:41:19"}
{"level":"error","message":"Error in User.create: duplicate key value violates unique constraint \"users_pkey\"","service":"pulih-hati-backend","timestamp":"2025-05-26 00:43:33"}
{"level":"error","message":"Stack trace: error: duplicate key value violates unique constraint \"users_pkey\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:17:22)\n    at async exports.register (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:24:12)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:43:33"}
{"level":"error","message":"error: duplicate key value violates unique constraint \"users_pkey\"\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.create (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:17:22)\n    at async exports.register (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\authController.js:24:12)","service":"pulih-hati-backend","timestamp":"2025-05-26 00:43:33"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-05-26 02:00:04"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-26 02:02:09"}
{"level":"error","message":"Database query error (attempt 1/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 02:02:50"}
{"level":"error","message":"Database query error (attempt 2/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 02:02:57"}
{"level":"error","message":"Database query error (attempt 3/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 02:03:09"}
{"level":"error","message":"Error in User.findById: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 02:03:09"}
{"level":"error","message":"Stack trace: Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async testUserUpdate (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\test-simple-update.js:14:26)","service":"pulih-hati-backend","timestamp":"2025-05-26 02:03:09"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-05-26 02:28:44"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:56:25"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:56:41"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:03"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:03"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:03"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:03"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:24"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:40"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:57:53"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:02"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:02"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:02"}
{"level":"error","message":"Transaction error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:02"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:04"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:04"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:09"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:13"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:24"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:24"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:25"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:25"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:29"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:29"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:31"}
{"level":"error","message":"Error in User.findById: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:31"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:31"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:40"}
{"level":"error","message":"Transaction error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:40"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:41"}
{"level":"error","message":"Transaction error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:41"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:45"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-05-26 02:58:45"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:01:46"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:02"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:24"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:24"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:24"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:24"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:43"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:52"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:02:59"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:08"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:21"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:21"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:21"}
{"level":"error","message":"Transaction error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:21"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:30"}
{"level":"error","message":"Error in User.findById: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:30"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:30"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:31"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:45"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:03:47"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:04:01"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:04:03"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:04:09"}
{"level":"error","message":"Error in User.findById: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:04:09"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.findById (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:37:22)\n    at async exports.protect (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\middleware\\auth.js:20:22)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:04:09"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-05-26 03:04:16"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:05:03"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:05:19"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:05:41"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:05:41"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:05:41"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:05:41"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:06:00"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:06:16"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:06:38"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:06:38"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:06:38"}
{"level":"error","message":"Transaction error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:06:38"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:05"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:21"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:43"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:43"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:43"}
{"level":"error","message":"Transaction error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:43"}
{"level":"error","message":"Error uploading avatar: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:43"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:07:43"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:10:39"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:10:55"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:11:17"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:11:17"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:11:17"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:11:17"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:11:35"}
{"level":"error","message":"Database query error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:11:51"}
{"level":"error","message":"Database query error (attempt 3/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:12:13"}
{"level":"error","message":"Error updating user 3: timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:12:13"}
{"level":"error","message":"Stack trace: Error: timeout exceeded when trying to connect\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg-pool\\index.js:45:11\n    at async queryWithRetry (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:57:16)\n    at async User.update (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\models\\User.js:182:22)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:69:27\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.uploadAvatar (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\uploadController.js:23:20)","service":"pulih-hati-backend","timestamp":"2025-05-26 03:12:13"}
{"level":"error","message":"Transaction error (attempt 2/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-26 03:12:13"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-05-26 04:54:37"}
{"level":"error","message":"Database query error (attempt 1/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 04:55:14"}
{"level":"error","message":"Database query error (attempt 2/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 04:55:21"}
{"level":"error","message":"Database query error (attempt 3/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 04:55:33"}
{"level":"error","message":"Database query error (attempt 1/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 04:57:56"}
{"level":"error","message":"Database query error (attempt 2/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 04:58:02"}
{"level":"error","message":"Database query error (attempt 3/3): SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"pulih-hati-backend","timestamp":"2025-05-26 04:58:14"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-26 05:13:46"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-05-26 05:23:37"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-29 20:26:11"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-05-29 20:27:22"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-05-29 20:28:25"}
<<<<<<< HEAD
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-06-01 14:03:27"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-06-01 14:05:41"}
{"level":"error","message":"Gagal menghubungi chatbot Flask: Request failed with status code 500","service":"pulih-hati-backend","timestamp":"2025-06-03 20:00:31"}
{"level":"error","message":"Gagal menghubungi chatbot Flask: Request failed with status code 500","service":"pulih-hati-backend","timestamp":"2025-06-03 20:00:34"}
{"level":"error","message":"Gagal menghubungi chatbot Flask: Request failed with status code 500","service":"pulih-hati-backend","timestamp":"2025-06-03 20:01:44"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-07 20:16:29"}
=======
>>>>>>> a8e8d2fafdf25f3c9170e6ce3412b6fc42fe4bfe
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-06-10 12:00:23"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 12:25:09"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 12:25:09"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 12:25:10"}
{"level":"error","message":"Transaction error (attempt 1/3): column \"is_anonymous\" of relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-06-10 12:41:07"}
{"level":"error","message":"Transaction error (attempt 2/3): column \"is_anonymous\" of relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-06-10 12:41:13"}
{"level":"error","message":"Transaction error (attempt 3/3): column \"is_anonymous\" of relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-06-10 12:41:26"}
{"level":"error","message":"Error creating post: column \"is_anonymous\" of relation \"posts\" does not exist","service":"pulih-hati-backend","timestamp":"2025-06-10 12:41:26"}
{"level":"error","message":"Stack trace: error: column \"is_anonymous\" of relation \"posts\" does not exist\n    at C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:155:22\n    at async transaction (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\config\\db.js:163:22)\n    at async exports.createPost (C:\\Users\\<USER>\\dicoding\\PulihHati-Backend\\controllers\\safeSpaceController.js:153:18)","service":"pulih-hati-backend","timestamp":"2025-06-10 12:41:26"}
{"level":"error","message":"Error closing pool: Called end on pool more than once","service":"pulih-hati-backend","timestamp":"2025-06-10 12:41:26"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-06-10 14:25:06"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 14:34:35"}
{"level":"error","message":"Transaction error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 14:34:36"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 14:34:52"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 14:34:54"}
{"level":"error","message":"Database query error (attempt 1/3): timeout exceeded when trying to connect","service":"pulih-hati-backend","timestamp":"2025-06-10 14:34:54"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-06-10 14:36:58"}
{"level":"error","message":"Redis connection error: ","service":"pulih-hati-backend","timestamp":"2025-06-10 14:55:22"}
{"level":"error","message":"Redis connection error: ","service":"pulih-hati-backend","timestamp":"2025-06-10 14:59:29"}
{"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::5000","service":"pulih-hati-backend","timestamp":"2025-06-10 14:59:29"}
{"level":"error","message":"Database query error (attempt 1/3): Connection terminated due to connection timeout","service":"pulih-hati-backend","timestamp":"2025-06-10 15:02:52"}
