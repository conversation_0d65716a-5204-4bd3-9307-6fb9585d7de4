const axios = require('axios');

async function createTestUser() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('👤 Creating test user...\n');
  
  try {
    const response = await axios.post(`${baseURL}/auth/register`, {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('✅ Test user created successfully!');
    console.log(`   - Name: ${response.data.user.name}`);
    console.log(`   - Email: ${response.data.user.email}`);
    console.log(`   - Token: ${response.data.token.substring(0, 20)}...`);
    
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      console.log('ℹ️ Test user already exists, that\'s fine!');
    } else {
      console.error('❌ Failed to create test user:', error.response?.data || error.message);
    }
  }
}

createTestUser();
